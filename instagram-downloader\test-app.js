// Simple test script to verify the application is working
import fetch from 'node-fetch';

const testAPI = async () => {
  try {
    console.log('Testing Instagram Downloader API...\n');
    
    // Test health endpoint
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch('http://localhost:3001/api/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.message);
    
    // Test with invalid URL
    console.log('\n2. Testing with invalid URL...');
    const invalidResponse = await fetch('http://localhost:3001/api/download', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ url: 'invalid-url' })
    });
    const invalidData = await invalidResponse.json();
    console.log('✅ Invalid URL handling:', invalidData.message);
    
    // Test with missing URL
    console.log('\n3. Testing with missing URL...');
    const missingResponse = await fetch('http://localhost:3001/api/download', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    });
    const missingData = await missingResponse.json();
    console.log('✅ Missing URL handling:', missingData.message);
    
    console.log('\n🎉 All API tests passed! The application is ready to use.');
    console.log('\nTo use the app:');
    console.log('1. Open http://localhost:5173 in your browser');
    console.log('2. Paste a valid Instagram URL');
    console.log('3. Click "Download Media"');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\nMake sure both servers are running:');
    console.log('- Frontend: npx vite');
    console.log('- Backend: node server.js');
  }
};

testAPI();
