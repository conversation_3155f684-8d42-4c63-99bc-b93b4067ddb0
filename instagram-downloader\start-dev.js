import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Starting Instagram Downloader...\n');

// Start backend server
console.log('📡 Starting backend server...');
const backend = spawn('node', ['server.js'], {
  cwd: __dirname,
  stdio: 'pipe'
});

backend.stdout.on('data', (data) => {
  console.log(`[Backend] ${data.toString().trim()}`);
});

backend.stderr.on('data', (data) => {
  console.error(`[Backend Error] ${data.toString().trim()}`);
});

// Wait a moment for backend to start, then start frontend
setTimeout(() => {
  console.log('🎨 Starting frontend server...');
  const frontend = spawn('npx', ['vite'], {
    cwd: __dirname,
    stdio: 'pipe'
  });

  frontend.stdout.on('data', (data) => {
    const output = data.toString().trim();
    console.log(`[Frontend] ${output}`);
    
    // Open browser when Vite is ready
    if (output.includes('Local:')) {
      console.log('\n🎉 Application is ready!');
      console.log('📱 Frontend: http://localhost:5173');
      console.log('🔧 Backend API: http://localhost:3001');
      console.log('\nPress Ctrl+C to stop both servers');
    }
  });

  frontend.stderr.on('data', (data) => {
    console.error(`[Frontend Error] ${data.toString().trim()}`);
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down servers...');
    backend.kill();
    frontend.kill();
    process.exit(0);
  });

}, 2000);

// Handle backend errors
backend.on('error', (error) => {
  console.error('❌ Failed to start backend:', error.message);
  process.exit(1);
});
