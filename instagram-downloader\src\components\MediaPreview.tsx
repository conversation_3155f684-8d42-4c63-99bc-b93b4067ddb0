import { useState } from 'react';

interface PostInfo {
  owner_username: string;
  owner_fullname: string;
  is_verified: boolean;
  is_private: boolean;
  likes: number;
  is_ad: boolean;
}

interface MediaDetail {
  type: 'video' | 'image';
  dimensions: {
    height: string;
    width: string;
  };
  video_view_count?: number;
  url: string;
  thumbnail?: string;
}

interface InstagramData {
  results_number: number;
  post_info: PostInfo;
  url_list: string[];
  media_details: MediaDetail[];
}

interface MediaPreviewProps {
  data: InstagramData;
  onReset: () => void;
}

const MediaPreview = ({ data, onReset }: MediaPreviewProps) => {
  const [loadingImages, setLoadingImages] = useState<{ [key: number]: boolean }>({});

  // Function to get proxied image URL
  const getProxiedImageUrl = (originalUrl: string) => {
    return `http://localhost:3001/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;
  };

  const handleImageLoad = (index: number) => {
    setLoadingImages(prev => ({ ...prev, [index]: false }));
  };

  const handleImageLoadStart = (index: number) => {
    setLoadingImages(prev => ({ ...prev, [index]: true }));
  };

  const downloadFile = async (url: string, filename: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    }
  };

  const downloadAll = () => {
    data.media_details.forEach((media, index) => {
      const extension = media.type === 'video' ? 'mp4' : 'jpg';
      const filename = `instagram_${data.post_info.owner_username}_${index + 1}.${extension}`;
      setTimeout(() => downloadFile(media.url, filename), index * 500);
    });
  };

  return (
    <div className="space-y-8 animate-in slide-in-from-bottom-4 duration-500">
      {/* Success Message */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center animate-pulse-glow">
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-green-800">Content Found!</h3>
            <p className="text-green-600 text-sm">Your Instagram content is ready for download</p>
          </div>
        </div>
      </div>

      {/* Post Info */}
      <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg animate-float">
                {data.post_info.owner_username.charAt(0).toUpperCase()}
              </div>
              {data.post_info.is_verified && (
                <div className="absolute -top-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-1">
                @{data.post_info.owner_username}
              </h3>
              <p className="text-gray-600">{data.post_info.owner_fullname}</p>
            </div>
          </div>
          <div className="text-right space-y-2">
            <div className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
              {data.results_number} file{data.results_number > 1 ? 's' : ''}
            </div>
            <div className="flex items-center justify-end space-x-2 text-red-500">
              <svg className="w-5 h-5 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
              </svg>
              <span className="font-semibold">{data.post_info.likes.toLocaleString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Media Grid */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {data.media_details.map((media, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
            <div className="aspect-square relative">
              {loadingImages[index] && (
                <div className="absolute inset-0 bg-gray-100 animate-pulse flex items-center justify-center">
                  <div className="text-gray-400 text-sm">Loading...</div>
                </div>
              )}
              {media.type === 'video' ? (
                <video
                  src={getProxiedImageUrl(media.url)}
                  poster={media.thumbnail ? getProxiedImageUrl(media.thumbnail) : undefined}
                  controls
                  className="w-full h-full object-cover"
                  crossOrigin="anonymous"
                  onLoadStart={() => handleImageLoadStart(index)}
                  onLoadedData={() => handleImageLoad(index)}
                />
              ) : (
                <img
                  src={getProxiedImageUrl(media.url)}
                  alt={`Media ${index + 1}`}
                  className="w-full h-full object-cover"
                  crossOrigin="anonymous"
                  onLoadStart={() => handleImageLoadStart(index)}
                  onLoad={() => handleImageLoad(index)}
                  onError={(e) => {
                    handleImageLoad(index);
                    // Fallback to original URL if proxy fails
                    const target = e.target as HTMLImageElement;
                    if (target.src.includes('/api/proxy-image')) {
                      target.src = media.url;
                    }
                  }}
                />
              )}
              <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm text-gray-700 px-2 py-1 rounded-lg text-xs font-medium shadow-sm">
                {media.type === 'video' ? (
                  <div className="flex items-center space-x-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                    <span>Video</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                    </svg>
                    <span>Photo</span>
                  </div>
                )}
              </div>
            </div>
            <div className="p-4">
              <div className="flex justify-between items-center mb-3">
                <span className="text-xs text-gray-500 font-medium">
                  {media.dimensions.width} × {media.dimensions.height}
                </span>
                {media.type === 'video' && media.video_view_count && (
                  <span className="text-xs text-gray-500 flex items-center space-x-1">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                    </svg>
                    <span>{media.video_view_count.toLocaleString()}</span>
                  </span>
                )}
              </div>
              <button
                onClick={() => {
                  const extension = media.type === 'video' ? 'mp4' : 'jpg';
                  const filename = `instagram_${data.post_info.owner_username}_${index + 1}.${extension}`;
                  downloadFile(media.url, filename);
                }}
                className="w-full bg-emerald-500 hover:bg-emerald-600 text-white text-sm font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg shadow-emerald-500/25"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>Download {media.type === 'video' ? 'Video' : 'Image'}</span>
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        {data.results_number > 1 && (
          <button
            onClick={downloadAll}
            className="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg shadow-blue-500/25"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3M4 7h16" />
            </svg>
            <span>Download All ({data.results_number} files)</span>
          </button>
        )}
        <button
          onClick={onReset}
          className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-all duration-200 border border-gray-200"
        >
          Download Another
        </button>
      </div>
    </div>
  );
};

export default MediaPreview;
