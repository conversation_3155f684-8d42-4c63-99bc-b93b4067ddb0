interface PostInfo {
  owner_username: string;
  owner_fullname: string;
  is_verified: boolean;
  is_private: boolean;
  likes: number;
  is_ad: boolean;
}

interface MediaDetail {
  type: 'video' | 'image';
  dimensions: {
    height: string;
    width: string;
  };
  video_view_count?: number;
  url: string;
  thumbnail?: string;
}

interface InstagramData {
  results_number: number;
  post_info: PostInfo;
  url_list: string[];
  media_details: MediaDetail[];
}

interface MediaPreviewProps {
  data: InstagramData;
  onReset: () => void;
}

const MediaPreview = ({ data, onReset }: MediaPreviewProps) => {
  const downloadFile = async (url: string, filename: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    }
  };

  const downloadAll = () => {
    data.media_details.forEach((media, index) => {
      const extension = media.type === 'video' ? 'mp4' : 'jpg';
      const filename = `instagram_${data.post_info.owner_username}_${index + 1}.${extension}`;
      setTimeout(() => downloadFile(media.url, filename), index * 500);
    });
  };

  return (
    <div className="space-y-6">
      {/* Post Info */}
      <div className="bg-gray-50 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">
              {data.post_info.owner_username.charAt(0).toUpperCase()}
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">
                @{data.post_info.owner_username}
                {data.post_info.is_verified && (
                  <span className="ml-1 text-blue-500">✓</span>
                )}
              </h3>
              <p className="text-gray-600 text-sm">{data.post_info.owner_fullname}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">
              {data.results_number} media file{data.results_number > 1 ? 's' : ''}
            </p>
            <p className="text-sm text-gray-600">
              ❤️ {data.post_info.likes.toLocaleString()} likes
            </p>
          </div>
        </div>
      </div>

      {/* Media Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {data.media_details.map((media, index) => (
          <div key={index} className="bg-gray-50 rounded-xl overflow-hidden">
            <div className="aspect-square relative">
              {media.type === 'video' ? (
                <video
                  src={media.url}
                  poster={media.thumbnail}
                  controls
                  className="w-full h-full object-cover"
                />
              ) : (
                <img
                  src={media.url}
                  alt={`Media ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              )}
              <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                {media.type === 'video' ? '🎥' : '📷'} {media.type}
              </div>
            </div>
            <div className="p-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">
                  {media.dimensions.width} × {media.dimensions.height}
                </span>
                {media.type === 'video' && media.video_view_count && (
                  <span className="text-sm text-gray-600">
                    👁️ {media.video_view_count.toLocaleString()}
                  </span>
                )}
              </div>
              <button
                onClick={() => {
                  const extension = media.type === 'video' ? 'mp4' : 'jpg';
                  const filename = `instagram_${data.post_info.owner_username}_${index + 1}.${extension}`;
                  downloadFile(media.url, filename);
                }}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors"
              >
                Download {media.type === 'video' ? 'Video' : 'Image'}
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        {data.results_number > 1 && (
          <button
            onClick={downloadAll}
            className="flex-1 bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-xl transition-colors"
          >
            Download All ({data.results_number} files)
          </button>
        )}
        <button
          onClick={onReset}
          className="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-colors"
        >
          Download Another
        </button>
      </div>
    </div>
  );
};

export default MediaPreview;
