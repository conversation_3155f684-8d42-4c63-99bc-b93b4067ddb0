import { useState } from 'react';

interface PostInfo {
  owner_username: string;
  owner_fullname: string;
  is_verified: boolean;
  is_private: boolean;
  likes: number;
  is_ad: boolean;
}

interface MediaDetail {
  type: 'video' | 'image';
  dimensions: {
    height: string;
    width: string;
  };
  video_view_count?: number;
  url: string;
  thumbnail?: string;
}

interface InstagramData {
  results_number: number;
  post_info: PostInfo;
  url_list: string[];
  media_details: MediaDetail[];
}

interface MediaPreviewProps {
  data: InstagramData;
  onReset: () => void;
}

const MediaPreview = ({ data, onReset }: MediaPreviewProps) => {
  const [loadingImages, setLoadingImages] = useState<{ [key: number]: boolean }>({});

  // Function to get proxied image URL
  const getProxiedImageUrl = (originalUrl: string) => {
    return `http://localhost:3001/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;
  };

  const handleImageLoad = (index: number) => {
    setLoadingImages(prev => ({ ...prev, [index]: false }));
  };

  const handleImageLoadStart = (index: number) => {
    setLoadingImages(prev => ({ ...prev, [index]: true }));
  };

  const downloadFile = async (url: string, filename: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    }
  };

  const downloadAll = () => {
    data.media_details.forEach((media, index) => {
      const extension = media.type === 'video' ? 'mp4' : 'jpg';
      const filename = `instagram_${data.post_info.owner_username}_${index + 1}.${extension}`;
      setTimeout(() => downloadFile(media.url, filename), index * 500);
    });
  };

  return (
    <div className="space-y-6">
      {/* Post Info */}
      <div className="bg-gray-900 border border-gray-700 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-400 rounded-full flex items-center justify-center text-black font-bold text-sm">
              {data.post_info.owner_username.charAt(0).toUpperCase()}
            </div>
            <div>
              <h3 className="font-medium text-white text-sm">
                @{data.post_info.owner_username}
                {data.post_info.is_verified && (
                  <span className="ml-1 text-blue-400">✓</span>
                )}
              </h3>
              <p className="text-gray-400 text-xs">{data.post_info.owner_fullname}</p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-xs text-gray-400">
              {data.results_number} file{data.results_number > 1 ? 's' : ''}
            </p>
            <p className="text-xs text-gray-400">
              ❤️ {data.post_info.likes.toLocaleString()}
            </p>
          </div>
        </div>
      </div>

      {/* Media Grid */}
      <div className="grid gap-3 grid-cols-1 sm:grid-cols-2">
        {data.media_details.map((media, index) => (
          <div key={index} className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden">
            <div className="aspect-square relative">
              {loadingImages[index] && (
                <div className="absolute inset-0 bg-gray-800 animate-pulse flex items-center justify-center">
                  <div className="text-gray-500 text-sm">Loading...</div>
                </div>
              )}
              {media.type === 'video' ? (
                <video
                  src={getProxiedImageUrl(media.url)}
                  poster={media.thumbnail ? getProxiedImageUrl(media.thumbnail) : undefined}
                  controls
                  className="w-full h-full object-cover"
                  crossOrigin="anonymous"
                  onLoadStart={() => handleImageLoadStart(index)}
                  onLoadedData={() => handleImageLoad(index)}
                />
              ) : (
                <img
                  src={getProxiedImageUrl(media.url)}
                  alt={`Media ${index + 1}`}
                  className="w-full h-full object-cover"
                  crossOrigin="anonymous"
                  onLoadStart={() => handleImageLoadStart(index)}
                  onLoad={() => handleImageLoad(index)}
                  onError={(e) => {
                    handleImageLoad(index);
                    // Fallback to original URL if proxy fails
                    const target = e.target as HTMLImageElement;
                    if (target.src.includes('/api/proxy-image')) {
                      target.src = media.url;
                    }
                  }}
                />
              )}
              <div className="absolute top-2 right-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                {media.type === 'video' ? '🎥' : '📷'}
              </div>
            </div>
            <div className="p-3">
              <div className="flex justify-between items-center mb-3">
                <span className="text-xs text-gray-400">
                  {media.dimensions.width} × {media.dimensions.height}
                </span>
                {media.type === 'video' && media.video_view_count && (
                  <span className="text-xs text-gray-400">
                    👁️ {media.video_view_count.toLocaleString()}
                  </span>
                )}
              </div>
              <button
                onClick={() => {
                  const extension = media.type === 'video' ? 'mp4' : 'jpg';
                  const filename = `instagram_${data.post_info.owner_username}_${index + 1}.${extension}`;
                  downloadFile(media.url, filename);
                }}
                className="w-full bg-orange-400 hover:bg-orange-500 text-black text-sm font-medium py-2 px-4 rounded-lg transition-colors"
              >
                ⬇ Download
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col gap-3">
        {data.results_number > 1 && (
          <button
            onClick={downloadAll}
            className="w-full bg-gray-700 hover:bg-gray-600 text-white text-sm font-medium py-3 px-4 rounded-lg transition-colors"
          >
            Download All ({data.results_number} files)
          </button>
        )}
        <button
          onClick={onReset}
          className="w-full bg-gray-800 hover:bg-gray-700 text-gray-300 text-sm font-medium py-3 px-4 rounded-lg transition-colors border border-gray-600"
        >
          Download Another
        </button>
      </div>
    </div>
  );
};

export default MediaPreview;
