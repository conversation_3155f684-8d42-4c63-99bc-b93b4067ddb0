{"name": "instagram-downloader", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server.js", "start": "npm run server", "start-dev": "node start-dev.js", "test": "node test-app.js"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "express": "^5.1.0", "instagram-url-direct": "^2.0.7", "lucide-react": "^0.541.0", "node-fetch": "^3.3.2", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}