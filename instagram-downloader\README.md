# Instagram Downloader

A modern, fast, and user-friendly Instagram downloader built with React, TypeScript, and Node.js. Download Instagram posts, reels, and videos by simply pasting the URL.

## Features

- 🚀 **Fast & Easy**: Simply paste the Instagram URL and download media in seconds
- 🔒 **Safe & Secure**: Your privacy is protected - we don't store any data
- 📱 **All Formats**: Download images, videos, reels, and carousel posts
- 🎨 **Modern UI**: Beautiful, responsive design with Tailwind CSS
- ⚡ **Real-time Processing**: Instant media extraction and preview

## Tech Stack

- **Frontend**: React 19, TypeScript, Vite, Tailwind CSS
- **Backend**: Node.js, Express
- **Instagram API**: `instagram-url-direct` package

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd instagram-downloader
```

2. Install dependencies:
```bash
npm install
```

3. Start the development servers:

**Frontend (in one terminal):**
```bash
npx vite
```

**Backend (in another terminal):**
```bash
node server.js
```

4. Open your browser and navigate to `http://localhost:5173`

## Usage

1. Copy an Instagram post URL (e.g., `https://www.instagram.com/p/ABC123/`)
2. Paste it into the input field
3. Click "Download Media"
4. Preview the media and download individual files or all at once

## Supported URLs

- Instagram Posts: `https://www.instagram.com/p/[POST_ID]/`
- Instagram Reels: `https://www.instagram.com/reel/[REEL_ID]/`
- Instagram TV: `https://www.instagram.com/tv/[TV_ID]/`

## API Endpoints

- `GET /api/health` - Health check
- `POST /api/download` - Download Instagram media
- `GET /api/proxy-image?url=<image_url>` - Proxy images to bypass CORS restrictions

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.
