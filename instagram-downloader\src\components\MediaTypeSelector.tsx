import { useState } from 'react';

interface MediaTypeSelectorProps {
  selectedType: string;
  onTypeChange: (type: string) => void;
}

const MediaTypeSelector = ({ selectedType, onTypeChange }: MediaTypeSelectorProps) => {
  const mediaTypes = [
    { id: 'photo', label: 'Photos', icon: '📸', gradient: 'from-blue-500 to-purple-500', active: true },
    { id: 'video', label: 'Videos', icon: '🎥', gradient: 'from-red-500 to-pink-500', active: true },
    { id: 'reels', label: 'Reels', icon: '🎬', gradient: 'from-purple-500 to-indigo-500', active: true },
    { id: 'story', label: 'Stories', icon: '⭕', gradient: 'from-orange-500 to-red-500', active: false },
    { id: 'highlight', label: 'Highlights', icon: '✨', gradient: 'from-yellow-500 to-orange-500', active: false },
    { id: 'igtv', label: 'IGTV', icon: '📺', gradient: 'from-green-500 to-teal-500', active: false },
  ];

  return (
    <div className="mb-8">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
        <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v1a1 1 0 01-1 1h-1v9a2 2 0 01-2 2H6a2 2 0 01-2-2V7H3a1 1 0 01-1-1V5a1 1 0 011-1h4zM9 3v1h6V3H9zm0 4v9h6V7H9z" />
        </svg>
        <span>Choose Content Type</span>
      </h3>
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
        {mediaTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => type.active && onTypeChange(type.id)}
            className={`group relative overflow-hidden rounded-2xl p-4 transition-all duration-300 transform hover:scale-105 ${
              selectedType === type.id
                ? `bg-gradient-to-br ${type.gradient} text-white shadow-xl shadow-purple-500/25`
                : type.active
                ? 'bg-white hover:bg-gray-50 text-gray-700 shadow-lg border border-gray-100'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-60'
            }`}
            disabled={!type.active}
          >
            {selectedType === type.id && (
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
            )}
            <div className="relative flex flex-col items-center space-y-2">
              <span className="text-2xl">{type.icon}</span>
              <span className="text-sm font-medium">{type.label}</span>
              {!type.active && (
                <span className="text-xs opacity-75">Coming Soon</span>
              )}
            </div>
            {selectedType === type.id && (
              <div className="absolute top-2 right-2">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default MediaTypeSelector;
