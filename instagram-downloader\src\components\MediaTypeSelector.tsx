import { But<PERSON> } from "./ui/button"
import { Badge } from "./ui/badge"
import { Camera, Video, Film, Circle, Sparkles, Tv, Check } from 'lucide-react'

interface MediaTypeSelectorProps {
  selectedType: string;
  onTypeChange: (type: string) => void;
}

const MediaTypeSelector = ({ selectedType, onTypeChange }: MediaTypeSelectorProps) => {
  const mediaTypes = [
    {
      id: 'photo',
      label: 'Photo',
      icon: Camera,
      description: 'Single images',
      active: true
    },
    {
      id: 'video',
      label: 'Video',
      icon: Video,
      description: 'Video posts',
      active: true
    },
    {
      id: 'reels',
      label: 'Reels',
      icon: Film,
      description: 'Short videos',
      active: true
    },
    {
      id: 'story',
      label: 'Story',
      icon: Circle,
      description: '24h content',
      active: false
    },
    {
      id: 'highlight',
      label: 'Highlight',
      icon: Sparkles,
      description: 'Saved stories',
      active: false
    },
    {
      id: 'igtv',
      label: 'IGTV',
      icon: Tv,
      description: 'Long videos',
      active: false
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 bg-primary rounded-full"></div>
        <h3 className="text-lg font-semibold text-foreground">Content Type</h3>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
        {mediaTypes.map((type) => {
          const IconComponent = type.icon;
          const isSelected = selectedType === type.id;

          return (
            <Button
              key={type.id}
              variant={isSelected ? "default" : "outline"}
              className={`h-auto p-4 flex flex-col items-center space-y-2 relative transition-all duration-200 ${
                isSelected ? "bg-purple-600 text-white shadow-lg" : ""
              } ${
                !type.active ? "opacity-50 cursor-not-allowed" : ""
              } ${
                type.active && !isSelected ? "hover:bg-gray-50 hover:text-gray-900" : ""
              }`}
              onClick={() => type.active && onTypeChange(type.id)}
              disabled={!type.active}
            >
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <Check className="w-3 h-3" />
                </div>
              )}

              <IconComponent className="w-6 h-6" />
              <div className="text-center">
                <div className="text-sm font-medium">{type.label}</div>
                <div className="text-xs opacity-70">{type.description}</div>
              </div>

              {!type.active && (
                <Badge variant="secondary" className="text-xs">
                  Soon
                </Badge>
              )}
            </Button>
          );
        })}
      </div>
    </div>
  );
};

export default MediaTypeSelector;
