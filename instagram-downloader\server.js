import express from 'express';
import cors from 'cors';
import { instagramGetUrl } from 'instagram-url-direct';

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Validate Instagram URL
const isValidInstagramUrl = (url) => {
  const instagramRegex = /^https?:\/\/(www\.)?instagram\.com\/(p|tv|reel)\/[A-Za-z0-9_-]+\/?/;
  return instagramRegex.test(url);
};

// API endpoint to get Instagram media URLs
app.post('/api/download', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({ 
        error: 'URL is required',
        message: 'Please provide an Instagram URL' 
      });
    }

    if (!isValidInstagramUrl(url)) {
      return res.status(400).json({ 
        error: 'Invalid Instagram URL',
        message: 'Please provide a valid Instagram post URL (posts, reels, or TV)' 
      });
    }

    console.log('Processing Instagram URL:', url);
    
    // Get Instagram media data
    const data = await instagramGetUrl(url);
    
    if (!data || !data.url_list || data.url_list.length === 0) {
      return res.status(404).json({ 
        error: 'No media found',
        message: 'Could not extract media from this Instagram post' 
      });
    }

    // Return the processed data
    res.json({
      success: true,
      data: {
        results_number: data.results_number,
        post_info: data.post_info,
        url_list: data.url_list,
        media_details: data.media_details
      }
    });

  } catch (error) {
    console.error('Error processing Instagram URL:', error);
    
    // Handle specific error cases
    if (error.message.includes('private')) {
      return res.status(403).json({ 
        error: 'Private account',
        message: 'This Instagram account is private and cannot be accessed' 
      });
    }
    
    if (error.message.includes('not found')) {
      return res.status(404).json({ 
        error: 'Post not found',
        message: 'The Instagram post could not be found or may have been deleted' 
      });
    }

    res.status(500).json({ 
      error: 'Server error',
      message: 'An error occurred while processing the Instagram URL. Please try again.' 
    });
  }
});

// Image proxy endpoint to handle CORS issues
app.get('/api/proxy-image', async (req, res) => {
  try {
    const { url } = req.query;

    if (!url) {
      return res.status(400).json({
        error: 'URL parameter is required',
        message: 'Please provide an image URL to proxy'
      });
    }

    // Validate that it's an image URL
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    const isImageUrl = imageExtensions.some(ext =>
      url.toLowerCase().includes(ext) || url.includes('instagram.com')
    );

    if (!isImageUrl) {
      return res.status(400).json({
        error: 'Invalid image URL',
        message: 'URL must be a valid image'
      });
    }

    // Fetch the image
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'image/*,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`);
    }

    // Get content type
    const contentType = response.headers.get('content-type') || 'image/jpeg';

    // Set appropriate headers
    res.set({
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=3600',
      'Access-Control-Allow-Origin': '*',
    });

    // Pipe the image data
    const buffer = await response.arrayBuffer();
    res.send(Buffer.from(buffer));

  } catch (error) {
    console.error('Error proxying image:', error);
    res.status(500).json({
      error: 'Proxy error',
      message: 'Failed to proxy the image'
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Instagram Downloader API is running' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log(`API endpoint: http://localhost:${PORT}/api/download`);
});
