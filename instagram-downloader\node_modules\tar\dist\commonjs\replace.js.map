{"version": 3, "file": "replace.js", "sourceRoot": "", "sources": ["../../src/replace.ts"], "names": [], "mappings": ";;;;;;AAAA,SAAS;AACT,qDAAkE;AAElE,sDAAwB;AACxB,0DAA4B;AAC5B,2CAAoC;AACpC,uCAAgC;AAChC,uDAA+C;AAC/C,6CAIqB;AACrB,uCAA0C;AAE1C,kDAAkD;AAClD,mEAAmE;AACnE,iEAAiE;AACjE,iBAAiB;AACjB,4CAA4C;AAE5C,MAAM,WAAW,GAAG,CAAC,GAAuB,EAAE,KAAe,EAAE,EAAE;IAC/D,MAAM,CAAC,GAAG,IAAI,kBAAQ,CAAC,GAAG,CAAC,CAAA;IAE3B,IAAI,KAAK,GAAG,IAAI,CAAA;IAChB,IAAI,EAAE,CAAA;IACN,IAAI,QAAQ,CAAA;IAEZ,IAAI,CAAC;QACH,IAAI,CAAC;YACH,EAAE,GAAG,iBAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAClC,CAAC;QAAC,OAAO,EAAE,EAAE,CAAC;YACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrD,EAAE,GAAG,iBAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YAClC,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,CAAA;YACV,CAAC;QACH,CAAC;QAED,MAAM,EAAE,GAAG,iBAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;QAC3B,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAEjC,QAAQ,EAAE,KACR,QAAQ,GAAG,CAAC,EACZ,QAAQ,GAAG,EAAE,CAAC,IAAI,EAClB,QAAQ,IAAI,GAAG,EACf,CAAC;YACD,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,GAAG,EAAE,MAAM,IAAI,KAAK,EAAE,CAAC;gBAC9D,KAAK,GAAG,iBAAE,CAAC,QAAQ,CACjB,EAAE,EACF,OAAO,EACP,MAAM,EACN,OAAO,CAAC,MAAM,GAAG,MAAM,EACvB,QAAQ,GAAG,MAAM,CAClB,CAAA;gBAED,IACE,QAAQ,KAAK,CAAC;oBACd,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI;oBACnB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EACnB,CAAC;oBACD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;gBACzD,CAAC;gBAED,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,MAAM,QAAQ,CAAA;gBAChB,CAAC;YACH,CAAC;YAED,MAAM,CAAC,GAAG,IAAI,kBAAM,CAAC,OAAO,CAAC,CAAA;YAC7B,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBAClB,MAAK;YACP,CAAC;YACD,MAAM,cAAc,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA;YAC3D,IAAI,QAAQ,GAAG,cAAc,GAAG,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;gBAC9C,MAAK;YACP,CAAC;YACD,8DAA8D;YAC9D,8CAA8C;YAC9C,QAAQ,IAAI,cAAc,CAAA;YAC1B,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC9B,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAA;YAC7C,CAAC;QACH,CAAC;QACD,KAAK,GAAG,KAAK,CAAA;QAEb,UAAU,CAAC,GAAG,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,CAAC,CAAA;IACzC,CAAC;YAAS,CAAC;QACT,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC;gBACH,iBAAE,CAAC,SAAS,CAAC,EAAY,CAAC,CAAA;YAC5B,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC,CAAA,CAAC;QACjB,CAAC;IACH,CAAC;AACH,CAAC,CAAA;AAED,MAAM,UAAU,GAAG,CACjB,GAAuB,EACvB,CAAO,EACP,QAAgB,EAChB,EAAU,EACV,KAAe,EACf,EAAE;IACF,MAAM,MAAM,GAAG,IAAI,6BAAe,CAAC,GAAG,CAAC,IAAI,EAAE;QAC3C,EAAE,EAAE,EAAE;QACN,KAAK,EAAE,QAAQ;KAChB,CAAC,CAAA;IACF,CAAC,CAAC,IAAI,CAAC,MAAsC,CAAC,CAAA;IAC9C,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AACxB,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,CACnB,GAAmB,EACnB,KAAe,EACA,EAAE;IACjB,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACzB,MAAM,CAAC,GAAG,IAAI,cAAI,CAAC,GAAG,CAAC,CAAA;IAEvB,MAAM,MAAM,GAAG,CACb,EAAU,EACV,IAAY,EACZ,GAA8C,EAC9C,EAAE;QACF,MAAM,EAAE,GAAG,CAAC,EAAiB,EAAE,GAAY,EAAE,EAAE;YAC7C,IAAI,EAAE,EAAE,CAAC;gBACP,iBAAE,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;YAC5B,CAAC;iBAAM,CAAC;gBACN,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;YAChB,CAAC;QACH,CAAC,CAAA;QAED,IAAI,QAAQ,GAAG,CAAC,CAAA;QAChB,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACf,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACpB,CAAC;QAED,IAAI,MAAM,GAAG,CAAC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACjC,MAAM,MAAM,GAAG,CAAC,EAAiB,EAAE,KAAc,EAAQ,EAAE;YACzD,IAAI,EAAE,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;gBACvC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAA;YACf,CAAC;YACD,MAAM,IAAI,KAAK,CAAA;YACf,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC;gBAC1B,OAAO,iBAAE,CAAC,IAAI,CACZ,EAAE,EACF,OAAO,EACP,MAAM,EACN,OAAO,CAAC,MAAM,GAAG,MAAM,EACvB,QAAQ,GAAG,MAAM,EACjB,MAAM,CACP,CAAA;YACH,CAAC;YAED,IACE,QAAQ,KAAK,CAAC;gBACd,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI;gBACnB,OAAO,CAAC,CAAC,CAAC,KAAK,IAAI,EACnB,CAAC;gBACD,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC,CAAA;YAC9D,CAAC;YAED,mBAAmB;YACnB,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;gBACjB,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAC3B,CAAC;YAED,MAAM,CAAC,GAAG,IAAI,kBAAM,CAAC,OAAO,CAAC,CAAA;YAC7B,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC;gBAClB,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAC3B,CAAC;YAED,oBAAoB;YACpB,MAAM,cAAc,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,CAAA;YAC3D,IAAI,QAAQ,GAAG,cAAc,GAAG,GAAG,GAAG,IAAI,EAAE,CAAC;gBAC3C,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAC3B,CAAC;YAED,QAAQ,IAAI,cAAc,GAAG,GAAG,CAAA;YAChC,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAC3B,CAAC;YAED,IAAI,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;gBAC9B,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAA;YAC7C,CAAC;YACD,MAAM,GAAG,CAAC,CAAA;YACV,iBAAE,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;QAChD,CAAC,CAAA;QACD,iBAAE,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAA;IAChD,CAAC,CAAA;IAED,MAAM,OAAO,GAAG,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACpD,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QACrB,IAAI,IAAI,GAAG,IAAI,CAAA;QACf,MAAM,MAAM,GAAG,CACb,EAAiC,EACjC,EAAW,EACX,EAAE;YACF,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAChD,IAAI,GAAG,IAAI,CAAA;gBACX,OAAO,iBAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;YACxC,CAAC;YAED,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;gBACd,OAAO,MAAM,CAAC,EAAE,CAAC,CAAA;YACnB,CAAC;YAED,iBAAE,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;gBACtB,IAAI,EAAE,EAAE,CAAC;oBACP,OAAO,iBAAE,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAA;gBACvC,CAAC;gBAED,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE;oBACnC,IAAI,EAAE,EAAE,CAAC;wBACP,OAAO,MAAM,CAAC,EAAE,CAAC,CAAA;oBACnB,CAAC;oBACD,MAAM,MAAM,GAAG,IAAI,yBAAW,CAAC,GAAG,CAAC,IAAI,EAAE;wBACvC,EAAE,EAAE,EAAE;wBACN,KAAK,EAAE,QAAQ;qBAChB,CAAC,CAAA;oBACF,CAAC,CAAC,IAAI,CAAC,MAAsC,CAAC,CAAA;oBAC9C,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;oBAC1B,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;oBAC3B,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;gBACzB,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QACD,iBAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;IACjC,CAAC,CAAC,CAAA;IAEF,OAAO,OAAO,CAAA;AAChB,CAAC,CAAA;AAED,MAAM,YAAY,GAAG,CAAC,CAAO,EAAE,KAAe,EAAE,EAAE;IAChD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACnB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC3B,IAAA,cAAI,EAAC;gBACH,IAAI,EAAE,mBAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;aACnC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACb,CAAC;IACH,CAAC,CAAC,CAAA;IACF,CAAC,CAAC,GAAG,EAAE,CAAA;AACT,CAAC,CAAA;AAED,MAAM,aAAa,GAAG,KAAK,EACzB,CAAO,EACP,KAAe,EACA,EAAE;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;QAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YAC3B,MAAM,IAAA,cAAI,EAAC;gBACT,IAAI,EAAE,mBAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAChD,QAAQ,EAAE,IAAI;gBACd,WAAW,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;aACnC,CAAC,CAAA;QACJ,CAAC;aAAM,CAAC;YACN,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QACb,CAAC;IACH,CAAC;IACD,CAAC,CAAC,GAAG,EAAE,CAAA;AACT,CAAC,CAAA;AAEY,QAAA,OAAO,GAAG,IAAA,6BAAW,EAChC,WAAW,EACX,YAAY;AACZ,qBAAqB;AACrB,GAAU,EAAE;IACV,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAA;AACzC,CAAC,EACD,GAAU,EAAE;IACV,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAA;AACzC,CAAC;AACD,oBAAoB;AACpB,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;IACf,IAAI,CAAC,IAAA,mBAAM,EAAC,GAAG,CAAC,EAAE,CAAC;QACjB,MAAM,IAAI,SAAS,CAAC,kBAAkB,CAAC,CAAA;IACzC,CAAC;IAED,IACE,GAAG,CAAC,IAAI;QACR,GAAG,CAAC,MAAM;QACV,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;QACxB,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EACzB,CAAC;QACD,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAA;IAC7D,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC;QACrB,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;IAC1D,CAAC;AACH,CAAC,CACF,CAAA", "sourcesContent": ["// tar -r\nimport { WriteStream, WriteStreamSync } from '@isaacs/fs-minipass'\nimport { Minipass } from 'minipass'\nimport fs from 'node:fs'\nimport path from 'node:path'\nimport { Header } from './header.js'\nimport { list } from './list.js'\nimport { makeCommand } from './make-command.js'\nimport {\n  isFile,\n  TarOptionsFile,\n  TarOptionsSyncFile,\n} from './options.js'\nimport { Pack, PackSync } from './pack.js'\n\n// starting at the head of the file, read a Header\n// If the checksum is invalid, that's our position to start writing\n// If it is, jump forward by the specified size (round up to 512)\n// and try again.\n// Write the new Pack stream starting there.\n\nconst replaceSync = (opt: TarOptionsSyncFile, files: string[]) => {\n  const p = new PackSync(opt)\n\n  let threw = true\n  let fd\n  let position\n\n  try {\n    try {\n      fd = fs.openSync(opt.file, 'r+')\n    } catch (er) {\n      if ((er as NodeJS.ErrnoException)?.code === 'ENOENT') {\n        fd = fs.openSync(opt.file, 'w+')\n      } else {\n        throw er\n      }\n    }\n\n    const st = fs.fstatSync(fd)\n    const headBuf = Buffer.alloc(512)\n\n    POSITION: for (\n      position = 0;\n      position < st.size;\n      position += 512\n    ) {\n      for (let bufPos = 0, bytes = 0; bufPos < 512; bufPos += bytes) {\n        bytes = fs.readSync(\n          fd,\n          headBuf,\n          bufPos,\n          headBuf.length - bufPos,\n          position + bufPos,\n        )\n\n        if (\n          position === 0 &&\n          headBuf[0] === 0x1f &&\n          headBuf[1] === 0x8b\n        ) {\n          throw new Error('cannot append to compressed archives')\n        }\n\n        if (!bytes) {\n          break POSITION\n        }\n      }\n\n      const h = new Header(headBuf)\n      if (!h.cksumValid) {\n        break\n      }\n      const entryBlockSize = 512 * Math.ceil((h.size || 0) / 512)\n      if (position + entryBlockSize + 512 > st.size) {\n        break\n      }\n      // the 512 for the header we just parsed will be added as well\n      // also jump ahead all the blocks for the body\n      position += entryBlockSize\n      if (opt.mtimeCache && h.mtime) {\n        opt.mtimeCache.set(String(h.path), h.mtime)\n      }\n    }\n    threw = false\n\n    streamSync(opt, p, position, fd, files)\n  } finally {\n    if (threw) {\n      try {\n        fs.closeSync(fd as number)\n      } catch (er) {}\n    }\n  }\n}\n\nconst streamSync = (\n  opt: TarOptionsSyncFile,\n  p: Pack,\n  position: number,\n  fd: number,\n  files: string[],\n) => {\n  const stream = new WriteStreamSync(opt.file, {\n    fd: fd,\n    start: position,\n  })\n  p.pipe(stream as unknown as Minipass.Writable)\n  addFilesSync(p, files)\n}\n\nconst replaceAsync = (\n  opt: TarOptionsFile,\n  files: string[],\n): Promise<void> => {\n  files = Array.from(files)\n  const p = new Pack(opt)\n\n  const getPos = (\n    fd: number,\n    size: number,\n    cb_: (er?: null | Error, pos?: number) => void,\n  ) => {\n    const cb = (er?: Error | null, pos?: number) => {\n      if (er) {\n        fs.close(fd, _ => cb_(er))\n      } else {\n        cb_(null, pos)\n      }\n    }\n\n    let position = 0\n    if (size === 0) {\n      return cb(null, 0)\n    }\n\n    let bufPos = 0\n    const headBuf = Buffer.alloc(512)\n    const onread = (er?: null | Error, bytes?: number): void => {\n      if (er || typeof bytes === 'undefined') {\n        return cb(er)\n      }\n      bufPos += bytes\n      if (bufPos < 512 && bytes) {\n        return fs.read(\n          fd,\n          headBuf,\n          bufPos,\n          headBuf.length - bufPos,\n          position + bufPos,\n          onread,\n        )\n      }\n\n      if (\n        position === 0 &&\n        headBuf[0] === 0x1f &&\n        headBuf[1] === 0x8b\n      ) {\n        return cb(new Error('cannot append to compressed archives'))\n      }\n\n      // truncated header\n      if (bufPos < 512) {\n        return cb(null, position)\n      }\n\n      const h = new Header(headBuf)\n      if (!h.cksumValid) {\n        return cb(null, position)\n      }\n\n      /* c8 ignore next */\n      const entryBlockSize = 512 * Math.ceil((h.size ?? 0) / 512)\n      if (position + entryBlockSize + 512 > size) {\n        return cb(null, position)\n      }\n\n      position += entryBlockSize + 512\n      if (position >= size) {\n        return cb(null, position)\n      }\n\n      if (opt.mtimeCache && h.mtime) {\n        opt.mtimeCache.set(String(h.path), h.mtime)\n      }\n      bufPos = 0\n      fs.read(fd, headBuf, 0, 512, position, onread)\n    }\n    fs.read(fd, headBuf, 0, 512, position, onread)\n  }\n\n  const promise = new Promise<void>((resolve, reject) => {\n    p.on('error', reject)\n    let flag = 'r+'\n    const onopen = (\n      er?: NodeJS.ErrnoException | null,\n      fd?: number,\n    ) => {\n      if (er && er.code === 'ENOENT' && flag === 'r+') {\n        flag = 'w+'\n        return fs.open(opt.file, flag, onopen)\n      }\n\n      if (er || !fd) {\n        return reject(er)\n      }\n\n      fs.fstat(fd, (er, st) => {\n        if (er) {\n          return fs.close(fd, () => reject(er))\n        }\n\n        getPos(fd, st.size, (er, position) => {\n          if (er) {\n            return reject(er)\n          }\n          const stream = new WriteStream(opt.file, {\n            fd: fd,\n            start: position,\n          })\n          p.pipe(stream as unknown as Minipass.Writable)\n          stream.on('error', reject)\n          stream.on('close', resolve)\n          addFilesAsync(p, files)\n        })\n      })\n    }\n    fs.open(opt.file, flag, onopen)\n  })\n\n  return promise\n}\n\nconst addFilesSync = (p: Pack, files: string[]) => {\n  files.forEach(file => {\n    if (file.charAt(0) === '@') {\n      list({\n        file: path.resolve(p.cwd, file.slice(1)),\n        sync: true,\n        noResume: true,\n        onReadEntry: entry => p.add(entry),\n      })\n    } else {\n      p.add(file)\n    }\n  })\n  p.end()\n}\n\nconst addFilesAsync = async (\n  p: Pack,\n  files: string[],\n): Promise<void> => {\n  for (let i = 0; i < files.length; i++) {\n    const file = String(files[i])\n    if (file.charAt(0) === '@') {\n      await list({\n        file: path.resolve(String(p.cwd), file.slice(1)),\n        noResume: true,\n        onReadEntry: entry => p.add(entry),\n      })\n    } else {\n      p.add(file)\n    }\n  }\n  p.end()\n}\n\nexport const replace = makeCommand(\n  replaceSync,\n  replaceAsync,\n  /* c8 ignore start */\n  (): never => {\n    throw new TypeError('file is required')\n  },\n  (): never => {\n    throw new TypeError('file is required')\n  },\n  /* c8 ignore stop */\n  (opt, entries) => {\n    if (!isFile(opt)) {\n      throw new TypeError('file is required')\n    }\n\n    if (\n      opt.gzip ||\n      opt.brotli ||\n      opt.file.endsWith('.br') ||\n      opt.file.endsWith('.tbr')\n    ) {\n      throw new TypeError('cannot append to compressed archives')\n    }\n\n    if (!entries?.length) {\n      throw new TypeError('no paths specified to add/replace')\n    }\n  },\n)\n"]}