interface ErrorMessageProps {
  message: string;
  onRetry: () => void;
}

const ErrorMessage = ({ message, onRetry }: ErrorMessageProps) => {
  return (
    <div className="bg-gray-900 border border-red-500/30 rounded-lg p-6 text-center">
      <div className="text-red-400 text-2xl mb-3">⚠️</div>
      <h3 className="text-red-400 font-medium mb-2">
        Something went wrong
      </h3>
      <p className="text-gray-400 text-sm mb-4">
        {message}
      </p>
      <button
        onClick={onRetry}
        className="bg-gray-700 hover:bg-gray-600 text-white text-sm py-2 px-4 rounded-lg transition-colors"
      >
        Try Again
      </button>
    </div>
  );
};

export default ErrorMessage;
