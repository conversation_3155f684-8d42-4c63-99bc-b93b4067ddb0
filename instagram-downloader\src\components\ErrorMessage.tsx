import { But<PERSON> } from "./ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface ErrorMessageProps {
  message: string;
  onRetry: () => void;
}

const ErrorMessage = ({ message, onRetry }: ErrorMessageProps) => {
  return (
    <Card className="border-red-200 bg-red-50">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2 text-red-800">
          <AlertTriangle className="w-5 h-5" />
          <span>Something went wrong</span>
        </CardTitle>
        <CardDescription className="text-red-700">
          {message}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2 text-sm text-gray-600">
          <p>• Make sure the Instagram URL is correct and public</p>
          <p>• Check that the post hasn't been deleted</p>
          <p>• Try refreshing the page if the issue persists</p>
        </div>

        <Button
          onClick={onRetry}
          variant="destructive"
          className="w-full sm:w-auto"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      </CardContent>
    </Card>
  );
};

export default ErrorMessage;
