import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Download, Clipboard, CheckCircle, AlertCircle, Link } from 'lucide-react'

interface UrlInputProps {
  url: string;
  onUrlChange: (url: string) => void;
  onSubmit: (url: string) => void;
  loading: boolean;
}

const UrlInput = ({ url, onUrlChange, onSubmit, loading }: UrlInputProps) => {
  const [inputValue, setInputValue] = useState(url);

  const isValidInstagramUrl = (url: string) => {
    const instagramRegex = /^https?:\/\/(www\.)?instagram\.com\/(p|tv|reel)\/[A-Za-z0-9_-]+\/?/;
    return instagramRegex.test(url);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && isValidInstagramUrl(inputValue.trim())) {
      onUrlChange(inputValue.trim());
      onSubmit(inputValue.trim());
    }
  };

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setInputValue(text);
      onUrlChange(text);
    } catch (err) {
      console.error('Failed to read clipboard:', err);
    }
  };

  const isValid = inputValue.trim() === '' || isValidInstagramUrl(inputValue.trim());

  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center space-x-2">
          <Link className="w-5 h-5 text-primary" />
          <span>Enter Instagram URL</span>
        </CardTitle>
        <CardDescription>
          Paste any Instagram post, reel, or video URL to download
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="relative">
            <Input
              type="url"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="https://www.instagram.com/p/example..."
              className={`pr-20 h-12 ${!isValid && inputValue.trim() !== '' ? 'border-destructive' : ''}`}
              disabled={loading}
            />

            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handlePaste}
                disabled={loading}
                className="h-8 w-8 p-0"
              >
                <Clipboard className="w-4 h-4" />
              </Button>

              {inputValue && isValid && (
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <CheckCircle className="w-3 h-3 text-white" />
                </div>
              )}
            </div>
          </div>

          {!isValid && inputValue.trim() !== '' && (
            <div className="flex items-center space-x-2 text-destructive bg-destructive/10 p-3 rounded-md border border-destructive/20">
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm">Please enter a valid Instagram URL</span>
            </div>
          )}

          <Button
            type="submit"
            disabled={!inputValue.trim() || !isValid || loading}
            className="w-full h-12"
            size="lg"
          >
            {loading ? (
              <>
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Download Content
              </>
            )}
          </Button>
        </form>

        <div className="mt-4 text-center">
          <p className="text-sm text-muted-foreground">
            Supports posts, reels, videos, and carousels
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default UrlInput;
