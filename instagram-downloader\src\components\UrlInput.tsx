import { useState } from 'react';

interface UrlInputProps {
  url: string;
  onUrlChange: (url: string) => void;
  onSubmit: (url: string) => void;
  loading: boolean;
}

const UrlInput = ({ url, onUrlChange, onSubmit, loading }: UrlInputProps) => {
  const [inputValue, setInputValue] = useState(url);

  const isValidInstagramUrl = (url: string) => {
    const instagramRegex = /^https?:\/\/(www\.)?instagram\.com\/(p|tv|reel)\/[A-Za-z0-9_-]+\/?/;
    return instagramRegex.test(url);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && isValidInstagramUrl(inputValue.trim())) {
      onUrlChange(inputValue.trim());
      onSubmit(inputValue.trim());
    }
  };

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setInputValue(text);
      onUrlChange(text);
    } catch (err) {
      console.error('Failed to read clipboard:', err);
    }
  };

  const isValid = inputValue.trim() === '' || isValidInstagramUrl(inputValue.trim());

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="relative">
          <input
            type="url"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="paste a link here..."
            className={`w-full px-4 py-4 text-white bg-gray-900 border border-gray-700 rounded-lg focus:outline-none focus:border-gray-500 transition-colors placeholder-gray-500 ${
              !isValid
                ? 'border-red-500 bg-red-900/20'
                : 'hover:border-gray-600'
            }`}
            disabled={loading}
          />
        </div>

        {!isValid && inputValue.trim() !== '' && (
          <p className="text-red-400 text-sm text-center">
            Please enter a valid Instagram URL
          </p>
        )}

        <button
          type="submit"
          disabled={!inputValue.trim() || !isValid || loading}
          className="w-full bg-orange-400 hover:bg-orange-500 text-black font-medium py-4 px-6 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2"
        >
          {loading ? (
            <>
              <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
              <span>fetching...</span>
            </>
          ) : (
            <>
              <span>⬇</span>
              <span>fetch media</span>
            </>
          )}
        </button>
      </form>
    </div>
  );
};

export default UrlInput;
