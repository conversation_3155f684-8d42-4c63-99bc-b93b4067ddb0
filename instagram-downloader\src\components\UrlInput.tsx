import { useState } from 'react';

interface UrlInputProps {
  url: string;
  onUrlChange: (url: string) => void;
  onSubmit: (url: string) => void;
  loading: boolean;
}

const UrlInput = ({ url, onUrlChange, onSubmit, loading }: UrlInputProps) => {
  const [inputValue, setInputValue] = useState(url);

  const isValidInstagramUrl = (url: string) => {
    const instagramRegex = /^https?:\/\/(www\.)?instagram\.com\/(p|tv|reel)\/[A-Za-z0-9_-]+\/?/;
    return instagramRegex.test(url);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim() && isValidInstagramUrl(inputValue.trim())) {
      onUrlChange(inputValue.trim());
      onSubmit(inputValue.trim());
    }
  };

  const handlePaste = async () => {
    try {
      const text = await navigator.clipboard.readText();
      setInputValue(text);
      onUrlChange(text);
    } catch (err) {
      console.error('Failed to read clipboard:', err);
    }
  };

  const isValid = inputValue.trim() === '' || isValidInstagramUrl(inputValue.trim());

  return (
    <div className="space-y-4">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="relative">
          <input
            type="url"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="e.g https://www.instagram.com/p/BHMNIXhUs/"
            className={`w-full px-4 py-4 text-gray-900 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 placeholder-gray-500 ${
              !isValid
                ? 'border-red-300 bg-red-50 focus:ring-red-500'
                : 'hover:border-gray-300'
            }`}
            disabled={loading}
          />
          <button
            type="button"
            onClick={handlePaste}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            disabled={loading}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </button>
        </div>

        {!isValid && inputValue.trim() !== '' && (
          <p className="text-red-500 text-sm">
            Please enter a valid Instagram URL
          </p>
        )}

        <button
          type="submit"
          disabled={!inputValue.trim() || !isValid || loading}
          className="w-full bg-emerald-500 hover:bg-emerald-600 text-white font-semibold py-4 px-6 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg shadow-emerald-500/25"
        >
          {loading ? (
            <>
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Processing...</span>
            </>
          ) : (
            <>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>Download Now</span>
            </>
          )}
        </button>
      </form>
    </div>
  );
};

export default UrlInput;
