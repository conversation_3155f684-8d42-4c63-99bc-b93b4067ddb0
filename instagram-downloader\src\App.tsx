import InstagramDownloader from './components/InstagramDownloader'
import { Instagram, Bookmark } from 'lucide-react'

function App() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <header className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl flex items-center justify-center">
              <Instagram className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Instagram Downloader</h1>
              <p className="text-sm text-gray-600">Download posts, reels, and stories</p>
            </div>
          </div>
          <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <Bookmark className="w-5 h-5 text-gray-500" />
          </button>
        </header>

        {/* Main Content */}
        <main>
          <InstagramDownloader />
        </main>

        {/* Footer */}
        <footer className="mt-16 text-center">
          <p className="text-sm text-gray-500">
            Built with modern web technologies • Free and open source
          </p>
        </footer>
      </div>
    </div>
  )
}

export default App
