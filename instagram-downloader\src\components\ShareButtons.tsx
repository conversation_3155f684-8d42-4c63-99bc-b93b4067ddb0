import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Share2, Twitter, Facebook, Linkedin, MessageCircle, Copy } from 'lucide-react'

interface ShareButtonsProps {
  url?: string;
}

const ShareButtons = ({ url }: ShareButtonsProps) => {
  const shareButtons = [
    {
      name: 'Twitter',
      icon: Twitter,
      color: 'hover:bg-sky-50 hover:text-sky-600 hover:border-sky-200',
      action: () => {
        window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=Check out this amazing Instagram Downloader!`);
      }
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200',
      action: () => {
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`);
      }
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      color: 'hover:bg-blue-50 hover:text-blue-700 hover:border-blue-200',
      action: () => {
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`);
      }
    },
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'hover:bg-green-50 hover:text-green-600 hover:border-green-200',
      action: () => {
        window.open(`https://wa.me/?text=Check out this amazing Instagram Downloader: ${encodeURIComponent(window.location.href)}`);
      }
    },
    {
      name: 'Copy Link',
      icon: Copy,
      color: 'hover:bg-gray-50 hover:text-gray-600 hover:border-gray-200',
      action: () => {
        navigator.clipboard.writeText(window.location.href);
        // You could add a toast notification here
        alert('Link copied to clipboard!');
      }
    }
  ];

  return (
    <Card className="mt-8">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center space-x-2">
          <Share2 className="w-5 h-5 text-primary" />
          <span>Share This Tool</span>
        </CardTitle>
        <CardDescription>
          Help others discover this Instagram downloader
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
          {shareButtons.map((button) => {
            const IconComponent = button.icon;

            return (
              <Button
                key={button.name}
                variant="outline"
                onClick={button.action}
                className={`h-auto p-4 flex flex-col items-center space-y-2 transition-all duration-200 ${button.color}`}
              >
                <IconComponent className="w-5 h-5" />
                <span className="text-xs font-medium">{button.name}</span>
              </Button>
            );
          })}
        </div>

        <div className="text-center mt-4">
          <p className="text-sm text-muted-foreground">
            Share with your network and help others discover this tool
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ShareButtons;
