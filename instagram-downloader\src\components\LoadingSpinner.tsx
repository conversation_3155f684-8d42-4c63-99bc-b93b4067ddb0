import { Card, CardContent } from "./ui/card"
import { Loader2, Instagram } from 'lucide-react'

const LoadingSpinner = () => {
  return (
    <Card>
      <CardContent className="flex flex-col items-center justify-center py-12">
        <div className="relative mb-6">
          <div className="w-16 h-16 border-4 border-gray-200 rounded-full"></div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-purple-600 rounded-full animate-spin"></div>

          {/* Center icon */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
              <Instagram className="w-4 h-4 text-white" />
            </div>
          </div>
        </div>

        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">
            Processing Your Request
          </h3>
          <p className="text-sm text-gray-600 max-w-sm">
            Fetching Instagram content and preparing your download
          </p>
        </div>

        {/* Progress dots */}
        <div className="flex justify-center space-x-1 mt-4">
          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce animation-delay-100"></div>
          <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce animation-delay-200"></div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoadingSpinner;
