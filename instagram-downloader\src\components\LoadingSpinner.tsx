const LoadingSpinner = () => {
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="relative">
        <div className="w-12 h-12 border-3 border-gray-200 border-t-emerald-500 rounded-full animate-spin"></div>
        <div className="absolute inset-0 w-12 h-12 border-3 border-transparent border-r-emerald-300 rounded-full animate-spin animation-delay-150"></div>
      </div>
      <div className="mt-6 text-center">
        <h3 className="text-lg font-semibold text-gray-700 mb-2">
          Processing your request...
        </h3>
        <p className="text-gray-500 text-sm">
          This may take a few moments
        </p>
      </div>
      <div className="mt-4 flex space-x-1">
        <div className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce animation-delay-100"></div>
        <div className="w-2 h-2 bg-emerald-500 rounded-full animate-bounce animation-delay-200"></div>
      </div>
    </div>
  );
};

export default LoadingSpinner;
