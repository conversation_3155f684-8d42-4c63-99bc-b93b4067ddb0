import { useState } from 'react';
import UrlInput from './UrlInput';
import MediaPreview from './MediaPreview';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';
import MediaTypeSelector from './MediaTypeSelector';
import ShareButtons from './ShareButtons';

interface PostInfo {
  owner_username: string;
  owner_fullname: string;
  is_verified: boolean;
  is_private: boolean;
  likes: number;
  is_ad: boolean;
}

interface MediaDetail {
  type: 'video' | 'image';
  dimensions: {
    height: string;
    width: string;
  };
  video_view_count?: number;
  url: string;
  thumbnail?: string;
}

interface InstagramData {
  results_number: number;
  post_info: PostInfo;
  url_list: string[];
  media_details: MediaDetail[];
}

const InstagramDownloader = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [data, setData] = useState<InstagramData | null>(null);
  const [selectedMediaType, setSelectedMediaType] = useState('photo');

  const handleSubmit = async (instagramUrl: string) => {
    setLoading(true);
    setError('');
    setData(null);

    try {
      const response = await fetch('http://localhost:3001/api/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: instagramUrl }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch Instagram data');
      }

      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setUrl('');
    setData(null);
    setError('');
  };

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Media Type Selector */}
      <MediaTypeSelector
        selectedType={selectedMediaType}
        onTypeChange={setSelectedMediaType}
      />

      {/* URL Input */}
      <UrlInput
        url={url}
        onUrlChange={setUrl}
        onSubmit={handleSubmit}
        loading={loading}
      />

      {/* Loading State */}
      {loading && <LoadingSpinner />}

      {/* Error State */}
      {error && <ErrorMessage message={error} onRetry={handleReset} />}

      {/* Media Preview */}
      {data && <MediaPreview data={data} onReset={handleReset} />}

      {/* Share Buttons */}
      <ShareButtons url={url} />
    </div>
  );
};

export default InstagramDownloader;
