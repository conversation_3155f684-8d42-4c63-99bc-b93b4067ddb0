import { useState } from 'react';
import UrlInput from './UrlInput';
import MediaPreview from './MediaPreview';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

interface PostInfo {
  owner_username: string;
  owner_fullname: string;
  is_verified: boolean;
  is_private: boolean;
  likes: number;
  is_ad: boolean;
}

interface MediaDetail {
  type: 'video' | 'image';
  dimensions: {
    height: string;
    width: string;
  };
  video_view_count?: number;
  url: string;
  thumbnail?: string;
}

interface InstagramData {
  results_number: number;
  post_info: PostInfo;
  url_list: string[];
  media_details: MediaDetail[];
}

const InstagramDownloader = () => {
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [data, setData] = useState<InstagramData | null>(null);

  const handleSubmit = async (instagramUrl: string) => {
    setLoading(true);
    setError('');
    setData(null);

    try {
      const response = await fetch('http://localhost:3001/api/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: instagramUrl }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch Instagram data');
      }

      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setUrl('');
    setData(null);
    setError('');
  };

  return (
    <div className="w-full">
      <UrlInput
        url={url}
        onUrlChange={setUrl}
        onSubmit={handleSubmit}
        loading={loading}
      />

      {loading && (
        <div className="mt-8">
          <LoadingSpinner />
        </div>
      )}

      {error && (
        <div className="mt-8">
          <ErrorMessage message={error} onRetry={handleReset} />
        </div>
      )}

      {data && (
        <div className="mt-8">
          <MediaPreview data={data} onReset={handleReset} />
        </div>
      )}

      {/* Supported platforms */}
      <div className="mt-8 text-center">
        <p className="text-gray-500 text-sm mb-4">supported platforms</p>
        <div className="flex items-center justify-center space-x-6">
          <div className="flex items-center space-x-2">
            <span className="text-pink-500">📱</span>
            <span className="text-gray-400 text-sm">TikTok</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-pink-500">📷</span>
            <span className="text-gray-400 text-sm">Instagram</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstagramDownloader;
